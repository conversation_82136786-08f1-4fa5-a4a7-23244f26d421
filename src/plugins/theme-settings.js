import $ from 'jquery'

// Reads data attributes for logo sources
const tflight = $('#logo_header').data('light')
const tfdark = $('#logo_header').data('dark')

/**
 * Menu Style toggles
 */
export function menuStyle() {
  $('.menu-style .icon-hover').on('click', () => {
    $('.layout-wrap').addClass('menu-style-icon').removeClass('menu-style-icon-default')
  })
  if ($('.layout-wrap').hasClass('menu-style-icon')) {
    $('.menu-style .icon-hover').prop('checked', true)
  }

  $('.menu-style .icon-default').on('click', () => {
    $('.layout-wrap').addClass('menu-style-icon-default').removeClass('menu-style-icon')
  })
  if ($('.layout-wrap').hasClass('menu-style-icon-default')) {
    $('.menu-style .icon-default').prop('checked', true)
  }

  $('.menu-style .menu-click').on('click', () => {
    $('.layout-wrap').removeClass('menu-style-icon menu-style-icon-default')
  })
  if (
    !$('.layout-wrap').hasClass('menu-style-icon') &&
    !$('.layout-wrap').hasClass('menu-style-icon-default')
  ) {
    $('.menu-style .menu-click').prop('checked', true)
  }
}

/**
 * Layout Width style toggles
 */
export function layoutWidth() {
  $('.layout-width .boxed').on('click', () => {
    $('.layout-wrap').addClass('layout-width-boxed')
  })
  if ($('.layout-wrap').hasClass('layout-width-boxed')) {
    $('.layout-width .boxed').prop('checked', true)
  }

  $('.layout-width .full').on('click', () => {
    $('.layout-wrap').removeClass('layout-width-boxed')
  })
  if (!$('.layout-wrap').hasClass('layout-width-boxed')) {
    $('.layout-width .full').prop('checked', true)
  }
}

/**
 * Menu position toggles
 */
export function menuPosition() {
  $('.menu-position .menu-fixed').on('click', () => {
    $('.layout-wrap').removeClass('menu-position-scrollable')
  })
  if (!$('.layout-wrap').hasClass('menu-position-scrollable')) {
    $('.menu-position .menu-fixed').prop('checked', true)
  }

  $('.menu-position .menu-scrollable').on('click', () => {
    $('.layout-wrap').addClass('menu-position-scrollable')
  })
  if ($('.layout-wrap').hasClass('menu-position-scrollable')) {
    $('.menu-position .menu-scrollable').prop('checked', true)
  }
}

/**
 * Header position toggles
 */
export function headerPosition() {
  $('.header-position .header-fixed').on('click', () => {
    $('.layout-wrap').removeClass('header-position-scrollable')
  })
  if (!$('.layout-wrap').hasClass('header-position-scrollable')) {
    $('.header-position .header-fixed').prop('checked', true)
  }

  $('.header-position .header-scrollable').on('click', () => {
    $('.layout-wrap').addClass('header-position-scrollable')
  })
  if ($('.layout-wrap').hasClass('header-position-scrollable')) {
    $('.header-position .header-scrollable').prop('checked', true)
  }
}

/**
 * Loader style toggles
 */
export function styleLoader() {
  $('.style-loader .style-loader-on').on('click', () => {
    $('.layout-wrap').removeClass('loader-off')
  })
  if (!$('.layout-wrap').hasClass('loader-off')) {
    $('.style-loader .style-loader-on').prop('checked', true)
  }

  $('.style-loader .style-loader-off').on('click', () => {
    $('.layout-wrap').addClass('loader-off')
  })
  if ($('.layout-wrap').hasClass('loader-off')) {
    $('.style-loader .style-loader-off').prop('checked', true)
  }
}

/**
 * Clears all style selections to defaults
 */
export function clearStyle() {
  $('.form-theme-style .button-clear-select').on('click', () => {
    $('body').removeClass('dark-theme')
    $('.theme-dark-light .light input').prop('checked', true)
    localStorage.toggled = 'light-theme'

    // Reset menu style
    $('.layout-wrap').removeClass('menu-style-icon menu-style-icon-default')
    $('.menu-style .menu-click').prop('checked', true)

    // Reset layout width
    $('.layout-wrap').removeClass('layout-width-boxed')
    $('.layout-width .full').prop('checked', true)

    // Reset positions
    $('.layout-wrap').removeClass('menu-position-scrollable header-position-scrollable')
    $('.menu-position .menu-fixed').prop('checked', true)
    $('.header-position .header-fixed').prop('checked', true)

    // Reset loader
    $('.layout-wrap').removeClass('loader-off')
    $('.style-loader .style-loader-on').prop('checked', true)
  })
}

/**
 * Theme color pickers
 */
export function colorsMenu() {
  const wrap = $('.colors-menu')
  wrap.find('.color-fff').on('click', () => {
    $('.layout-wrap').attr('data-menu-background', 'colors-menu-fff')
    $('#logo_header').attr('src', tfdark)
  })
  wrap.find('.color-1E293B').on('click', () => {
    $('.layout-wrap').attr('data-menu-background', 'colors-menu-1E293B')
    $('#logo_header').attr('src', tflight)
  })
  // ...repeat for other colors
}

export function colorsHeader() {
  $('.colors-header .color-fff').on('click', () => {
    $('.layout-wrap').attr('data-colors-header', 'colors-header-fff')
  })
  // ...other header colors
}

export function primaryTheme() {
  $('.colors-theme-primary .color-2377FC').on('click', () => {
    $('.layout-wrap').attr('data-theme-primary', 'theme-primary-2377FC')
  })
  // ...other primary colors
}

export function themeBackground() {
  $('.colors-theme-background .color-FFFFFF').on('click', () => {
    $('body').attr('data-theme-background', 'theme-background-FFFFFF')
  })
  // ...other background colors
}
