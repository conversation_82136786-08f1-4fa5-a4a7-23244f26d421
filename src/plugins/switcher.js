// src/plugins/switcher-plugin.js

// 1) Console‐shim: if console is missing, stub out all methods
export function consoleShim() {
  if (typeof window.console === 'undefined') {
    const methods = [
      'log',
      'warn',
      'debug',
      'info',
      'error',
      'time',
      'dir',
      'profile',
      'clear',
      'exception',
      'trace',
      'assert',
    ]
    window.console = {}
    methods.forEach((m) => (window.console[m] = () => {}))
  }
}

// 2) Inject the offcanvas HTML into #wrapper
export function injectOffcanvas() {
  const wrapper = document.querySelector('#wrapper')
  if (!wrapper) {
    console.warn('Could not find #wrapper element for theme switcher offcanvas')
    return
  }

  // Check if offcanvas already exists
  if (document.querySelector('#offcanvasRight')) {
    console.log('Theme switcher offcanvas already exists')
    return
  }

  wrapper.insertAdjacentHTML(
    'beforeend',
    `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel" data-bs-backdrop="true" data-bs-scroll="false" data-bs-keyboard="true">
      <div class="offcanvas-header">
        <h6 id="offcanvasRightLabel">Setting</h6>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="widget-tabs">
          <ul class="widget-menu-tab style-1">
            <li class="item-title active">
              <span class="inner">
                <div class="body-title">Theme Style</div>
              </span>
            </li>
            <li class="item-title">
              <span class="inner">
                <div class="body-title">Theme Colors</div>
              </span>
            </li>
          </ul>
          <div class="widget-content-tab">
            <div class="widget-content-inner active">
              <form class="form-theme-style">
                <fieldset class="theme-dark-light">
                  <div class="body-title mb-5">Theme</div>
                  <div class="radio-buttons">
                    <div class="item light">
                      <input class="light-mode" type="radio" name="theme-mode" id="theme-light" checked="">
                      <label for="theme-light" class=""><div class="body-title">Light</div></label>
                    </div>
                    <div class="item dark">
                      <input class="dark-mode" type="radio" name="theme-mode" id="theme-dark">
                      <label for="theme-dark" class=""><div class="body-title">Dark</div></label>
                    </div>
                  </div>
                </fieldset>
                <fieldset class="layout-width">
                  <div class="body-title mb-5">Layout width style</div>
                  <div class="radio-buttons">
                    <div class="item">
                      <input class="boxed" type="radio" name="width-style" id="width-style2" checked="">
                      <label for="width-style2" class=""><div class="body-title">Boxed</div></label>
                    </div>
                    <div class="item">
                      <input class="full" type="radio" name="width-style" id="width-style1">
                      <label for="width-style1" class=""><div class="body-title">Full width</div></label>
                    </div>
                  </div>
                </fieldset>
                <fieldset class="menu-style">
                  <div class="body-title mb-5">Vertical & Horizontal menu style</div>
                  <div class="radio-buttons">
                    <div class="item">
                      <input class="menu-click" type="radio" name="menu-style" id="menu-style1" checked="">
                      <label class="" for="menu-style1"><div class="body-title">Menu click</div></label>
                    </div>
                    <div class="item">
                      <input class="icon-hover" type="radio" name="menu-style" id="menu-style2">
                      <label class="" for="menu-style2"><div class="body-title">Icon hover</div></label>
                    </div>
                    <div class="item">
                      <input class="icon-default" type="radio" name="menu-style" id="menu-style3">
                      <label class="" for="menu-style3"><div class="body-title">Icon default</div></label>
                    </div>
                  </div>
                </fieldset>
                <fieldset class="menu-position">
                  <div class="body-title mb-5">Menu position</div>
                  <div class="radio-buttons">
                    <div class="item">
                      <input class="menu-fixed" type="radio" name="menu-position" id="menu-position1" checked="">
                      <label class="" for="menu-position1"><div class="body-title">Fixed</div></label>
                    </div>
                    <div class="item">
                      <input class="menu-scrollable" type="radio" name="menu-position" id="menu-position2">
                      <label class="" for="menu-position2"><div class="body-title">Scrollable</div></label>
                    </div>
                  </div>
                </fieldset>
                <fieldset class="header-position">
                  <div class="body-title mb-5">Header positions</div>
                  <div class="radio-buttons">
                    <div class="item">
                      <input class="header-fixed" type="radio" name="header-positions" id="header-positions1" checked="">
                      <label class="" for="header-positions1"><div class="body-title">Fixed</div></label>
                    </div>
                    <div class="item">
                      <input class="header-scrollable" type="radio" name="header-positions" id="header-positions2">
                      <label class="" for="header-positions2"><div class="body-title">Scrollable</div></label>
                    </div>
                  </div>
                </fieldset>
                <fieldset class="style-loader">
                  <div class="body-title mb-5">Loader</div>
                  <div class="radio-buttons">
                    <div class="item">
                      <input class="style-loader-on" type="radio" name="loader" id="loader1" checked="">
                      <label class="" for="loader1"><div class="body-title">Enable</div></label>
                    </div>
                    <div class="item">
                      <input class="style-loader-off" type="radio" name="loader" id="loader2">
                      <label class="" for="loader2"><div class="body-title">Disable</div></label>
                    </div>
                  </div>
                </fieldset>
                <div class="tf-button style-1 label-01 w-100 cursor-pointer w-full button-clear-select">Clear all</div>
              </form>
            </div>
            <div class="widget-content-inner">
              <form class="form-theme-color">
                <fieldset class="menu-color">
                  <div class="body-title mb-10">Menu Background color</div>
                  <div class="select-colors-theme colors-menu mb-10">
                    <div class="item color-161326 active default"></div>
                    <div class="item color-1E293B"></div>
                    <div class="item color-fff"></div>
                    <div class="item color-3A3043"></div>
                  </div>
                  <div class="text-tiny">Note:If you want to change color Menu dynamically change from below Theme Primary color picker</div>
                </fieldset>
                <fieldset class="">
                  <div class="body-title mb-10">Header Background color</div>
                  <div class="select-colors-theme colors-header mb-10">
                    <div class="item color-fff active default"></div>
                    <div class="item color-1E293B"></div>
                    <div class="item color-161326"></div>
                    <div class="item color-3A3043"></div>
                  </div>
                  <div class="text-tiny">Note:If you want to change color Header dynamically change from below Theme Primary color picker</div>
                </fieldset>
                <fieldset class="">
                  <div class="body-title mb-10">Theme Primary color</div>
                  <div class="select-colors-theme colors-theme-primary mb-10">
                    <div class="item color-161326 active default"></div>
                    <div class="item color-2377FC"></div>
                    <div class="item color-35988D"></div>
                    <div class="item color-7047D6"></div>
                  </div>
                </fieldset>
                <fieldset class="">
                  <div class="body-title mb-10">Theme Background color</div>
                  <div class="select-colors-theme colors-theme-background mb-10">
                    <div class="item color-FFFFFF active default"></div>
                    <div class="item color-252E3A"></div>
                    <div class="item color-1E1D2A"></div>
                    <div class="item color-1B2627"></div>
                  </div>
                </fieldset>
                <div class="tf-button style-1 label-01 w-100 cursor-pointer w-full button-clear-select">Clear all</div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  )

  console.log('Theme switcher offcanvas injected successfully')
}

// 3) Inject the little toggle button into .header-grid
export function injectHeaderToggle() {
  const headerGrid = document.querySelector('.header-grid')
  if (!headerGrid) return
  headerGrid.insertAdjacentHTML(
    'beforeend',
    `
    <div class="divider"></div>
    <div class="setting cursor-pointer" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight">
      <i class="icon-setting1"></i>
    </div>
  `,
  )
}

// 4) Theme functionality
function initializeThemeFunctionality() {
  // Import jQuery only - let Bootstrap handle itself
  import('jquery').then(({ default: $ }) => {
    // Wait for DOM elements to be ready
    setTimeout(() => {
      initializeThemeSettings($)
      // Don't manually initialize Bootstrap - let it handle data attributes
      console.log('Theme functionality initialized')
    }, 100)
  }).catch(error => {
    console.error('Failed to load jQuery:', error)
  })
}

function initializeThemeSettings($) {
  console.log('Initializing theme settings...')

  // Initialize tab functionality first
  initializeTabSwitching($)

  // Initialize close button functionality
  initializeCloseButton($)

  // Logo sources
  const tflight = $('#logo_header').data('light')
  const tfdark = $('#logo_header').data('dark')

  console.log('Logo sources:', { tflight, tfdark })

  // Dark/Light Theme Toggle
  $('.theme-dark-light .light input').on('click', () => {
    console.log('Light theme selected')
    $('body').removeClass('dark-theme')
    localStorage.toggled = 'light-theme'
  })

  $('.theme-dark-light .dark input').on('click', () => {
    console.log('Dark theme selected')
    $('body').addClass('dark-theme')
    localStorage.toggled = 'dark-theme'
  })

  // Initialize theme based on localStorage
  const savedTheme = localStorage.toggled
  if (savedTheme === 'dark-theme') {
    $('body').addClass('dark-theme')
    $('.theme-dark-light .dark input').prop('checked', true)
    $('.theme-dark-light .light input').prop('checked', false)
  } else {
    $('body').removeClass('dark-theme')
    $('.theme-dark-light .light input').prop('checked', true)
    $('.theme-dark-light .dark input').prop('checked', false)
  }

  console.log('Theme initialized:', savedTheme || 'light-theme')

  // Import and initialize theme settings functions
  import('../plugins/theme-settings.js').then(({
    menuStyle,
    layoutWidth,
    menuPosition,
    headerPosition,
    styleLoader,
    clearStyle
  }) => {
    console.log('Initializing theme settings functions...')

    // Initialize all theme setting functions
    menuStyle()
    layoutWidth()
    menuPosition()
    headerPosition()
    styleLoader()
    clearStyle()

    console.log('Theme settings functions initialized')
  }).catch(error => {
    console.error('Failed to load theme settings:', error)
  })

  // Menu Position
  $('.menu-position .menu-fixed').on('click', () => {
    $('.layout-wrap').removeClass('menu-position-scrollable')
  })
  if (!$('.layout-wrap').hasClass('menu-position-scrollable')) {
    $('.menu-position .menu-fixed').prop('checked', true)
  }

  $('.menu-position .menu-scrollable').on('click', () => {
    $('.layout-wrap').addClass('menu-position-scrollable')
  })
  if ($('.layout-wrap').hasClass('menu-position-scrollable')) {
    $('.menu-position .menu-scrollable').prop('checked', true)
  }

  // Header Position
  $('.header-position .header-fixed').on('click', () => {
    $('.layout-wrap').removeClass('header-position-scrollable')
  })
  if (!$('.layout-wrap').hasClass('header-position-scrollable')) {
    $('.header-position .header-fixed').prop('checked', true)
  }

  $('.header-position .header-scrollable').on('click', () => {
    $('.layout-wrap').addClass('header-position-scrollable')
  })
  if ($('.layout-wrap').hasClass('header-position-scrollable')) {
    $('.header-position .header-scrollable').prop('checked', true)
  }

  // Style Loader
  $('.style-loader .style-loader-on').on('click', () => {
    $('.layout-wrap').removeClass('loader-off')
  })
  if (!$('.layout-wrap').hasClass('loader-off')) {
    $('.style-loader .style-loader-on').prop('checked', true)
  }

  $('.style-loader .style-loader-off').on('click', () => {
    $('.layout-wrap').addClass('loader-off')
  })
  if ($('.layout-wrap').hasClass('loader-off')) {
    $('.style-loader .style-loader-off').prop('checked', true)
  }

  // Color selections
  console.log('Setting up color selection handlers...')

  // Menu colors
  $('.colors-menu .color-fff').on('click', function() {
    console.log('Menu color white selected')
    $('.colors-menu .item').removeClass('active')
    $(this).addClass('active')
    $('.layout-wrap').attr('data-menu-background', 'colors-menu-fff')
    $('#logo_header').attr('src', tfdark)
  })
  $('.colors-menu .color-1E293B').on('click', function() {
    console.log('Menu color dark blue selected')
    $('.colors-menu .item').removeClass('active')
    $(this).addClass('active')
    $('.layout-wrap').attr('data-menu-background', 'colors-menu-1E293B')
    $('#logo_header').attr('src', tflight)
  })
  $('.colors-menu .color-161326').on('click', function() {
    console.log('Menu color dark selected')
    $('.colors-menu .item').removeClass('active')
    $(this).addClass('active')
    $('.layout-wrap').attr('data-menu-background', 'colors-menu-161326')
    $('#logo_header').attr('src', tflight)
  })
  $('.colors-menu .color-3A3043').on('click', function() {
    console.log('Menu color purple selected')
    $('.colors-menu .item').removeClass('active')
    $(this).addClass('active')
    $('.layout-wrap').attr('data-menu-background', 'colors-menu-3A3043')
    $('#logo_header').attr('src', tflight)
  })

  // Header colors
  $('.colors-header .color-fff').on('click', () => {
    $('.layout-wrap').attr('data-colors-header', 'colors-header-fff')
  })
  $('.colors-header .color-1E293B').on('click', () => {
    $('.layout-wrap').attr('data-colors-header', 'colors-header-1E293B')
  })
  $('.colors-header .color-161326').on('click', () => {
    $('.layout-wrap').attr('data-colors-header', 'colors-header-161326')
  })
  $('.colors-header .color-3A3043').on('click', () => {
    $('.layout-wrap').attr('data-colors-header', 'colors-header-3A3043')
  })

  // Primary theme colors
  $('.colors-theme-primary .color-2377FC').on('click', () => {
    $('.layout-wrap').attr('data-theme-primary', 'theme-primary-2377FC')
  })
  $('.colors-theme-primary .color-161326').on('click', () => {
    $('.layout-wrap').attr('data-theme-primary', 'theme-primary-161326')
  })
  $('.colors-theme-primary .color-35988D').on('click', () => {
    $('.layout-wrap').attr('data-theme-primary', 'theme-primary-35988D')
  })
  $('.colors-theme-primary .color-7047D6').on('click', () => {
    $('.layout-wrap').attr('data-theme-primary', 'theme-primary-7047D6')
  })

  // Background theme colors
  $('.colors-theme-background .color-FFFFFF').on('click', () => {
    $('body').attr('data-theme-background', 'theme-background-FFFFFF')
  })
  $('.colors-theme-background .color-252E3A').on('click', () => {
    $('body').attr('data-theme-background', 'theme-background-252E3A')
  })
  $('.colors-theme-background .color-1E1D2A').on('click', () => {
    $('body').attr('data-theme-background', 'theme-background-1E1D2A')
  })
  $('.colors-theme-background .color-1B2627').on('click', () => {
    $('body').attr('data-theme-background', 'theme-background-1B2627')
  })

  // Clear all functionality
  $('.form-theme-style .button-clear-select').on('click', () => {
    console.log('Clear all style settings clicked')
    $('body').removeClass('dark-theme')
    $('.theme-dark-light .light input').prop('checked', true)
    localStorage.toggled = 'light-theme'

    // Reset layout
    $('.layout-wrap').removeClass(
      'layout-width-boxed menu-style-icon menu-style-icon-default menu-position-scrollable header-position-scrollable loader-off',
    )

    // Reset form selections
    $('.layout-width .full').prop('checked', true)
    $('.menu-style .menu-click').prop('checked', true)
    $('.menu-position .menu-fixed').prop('checked', true)
    $('.header-position .header-fixed').prop('checked', true)
    $('.style-loader .style-loader-on').prop('checked', true)

    console.log('Style settings cleared')
  })

  $('.form-theme-color .button-clear-select').on('click', () => {
    console.log('Clear all color settings clicked')
    // Reset colors
    $('.layout-wrap').removeAttr('data-menu-background data-colors-header data-theme-primary')
    $('body').removeAttr('data-theme-background')

    // Reset color selections
    $('.colors-menu .item').removeClass('active')
    $('.colors-menu .color-161326').addClass('active')
    $('.colors-header .item').removeClass('active')
    $('.colors-header .color-fff').addClass('active')
    $('.colors-theme-primary .item').removeClass('active')
    $('.colors-theme-primary .color-161326').addClass('active')
    $('.colors-theme-background .item').removeClass('active')
    $('.colors-theme-background .color-FFFFFF').addClass('active')

    // Reset logo
    $('#logo_header').attr('src', tflight)

    console.log('Color settings cleared')
  })

  console.log('Theme functionality initialized')
}

// Initialize tab switching functionality
function initializeTabSwitching($) {
  console.log('Initializing tab switching...')

  // Tab switching functionality
  $('.widget-menu-tab .item-title').on('click', function() {
    const $this = $(this)
    const index = $this.index()

    // Remove active class from all tabs and content
    $('.widget-menu-tab .item-title').removeClass('active')
    $('.widget-content-inner').removeClass('active')

    // Add active class to clicked tab and corresponding content
    $this.addClass('active')
    $('.widget-content-inner').eq(index).addClass('active')

    console.log('Tab switched to index:', index)
  })

  console.log('Tab switching initialized')
}

// Initialize close button functionality
function initializeCloseButton($) {
  console.log('Initializing close button...')

  // Close button functionality
  $('[data-bs-dismiss="offcanvas"]').on('click', function() {
    console.log('Close button clicked')
    const offcanvasElement = document.querySelector('#offcanvasRight')
    if (offcanvasElement) {
      // Try Bootstrap method first
      if (window.bootstrap && window.bootstrap.Offcanvas) {
        const offcanvas = window.bootstrap.Offcanvas.getInstance(offcanvasElement)
        if (offcanvas) {
          offcanvas.hide()
        }
      } else {
        // Fallback: manual close
        offcanvasElement.classList.remove('show')
        offcanvasElement.style.visibility = 'hidden'
        offcanvasElement.style.transform = 'translateX(100%)'

        // Remove backdrop
        const backdrop = document.querySelector('.offcanvas-backdrop')
        if (backdrop) {
          backdrop.remove()
        }
      }
    }
  })

  console.log('Close button initialized')
}

// 5) The plugin itself
export default {
  install(app) {
    console.log('SwitcherPlugin: Installing plugin...')

    // polyfill console right away
    consoleShim()

    // on root component mount, run our two injections
    app.mixin({
      mounted() {
        if (this !== this.$root) return

        console.log('SwitcherPlugin: Root component mounted, initializing...')
        console.log('SwitcherPlugin: Checking DOM elements...')
        console.log('  - #wrapper:', !!document.querySelector('#wrapper'))
        console.log('  - #offcanvasRight:', !!document.querySelector('#offcanvasRight'))

        // Use nextTick to ensure DOM is ready
        this.$nextTick(() => {
          setTimeout(() => {
            console.log('SwitcherPlugin: Starting injection...')
            injectOffcanvas()
            // Note: Button is now in NavBar template by default, no need to inject

            // Initialize theme functionality after injection
            setTimeout(() => {
              initializeThemeFunctionality()
            }, 300) // Increased delay for Bootstrap to be ready
          }, 200) // Increased delay for DOM to be ready
        })
      },
    })
  },
}
