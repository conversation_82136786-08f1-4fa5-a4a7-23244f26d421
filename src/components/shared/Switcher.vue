<script setup>
import { onMounted, ref } from 'vue'

// Active tab state
const activeTab = ref(0)

// Switch tabs
const switchTab = (index) => {
  activeTab.value = index
}

// Theme settings functions
const applyLayoutWidth = (isBoxed) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isBoxed) {
      layoutWrap.classList.add('layout-width-boxed')
      console.log('Applied boxed layout')
    } else {
      layoutWrap.classList.remove('layout-width-boxed')
      console.log('Applied full width layout')
    }
  }
}

const applyMenuStyle = (style) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    console.log(`Applying menu style: ${style}`)

    // Remove all menu style classes first
    layoutWrap.classList.remove('menu-style-icon', 'menu-style-icon-default')

    // Apply the selected style
    if (style === 'icon-hover') {
      layoutWrap.classList.add('menu-style-icon')
      console.log('✅ Applied icon hover menu style - sidebar will expand on hover')
    } else if (style === 'icon-default') {
      layoutWrap.classList.add('menu-style-icon-default')
      console.log('✅ Applied icon default menu style - sidebar shows icons only')
    } else {
      console.log('✅ Applied default menu style - full sidebar visible')
    }

    // Debug: Show current classes
    console.log('Current layout classes:', layoutWrap.className)
  } else {
    console.error('❌ Layout wrap element not found')
  }
}

const applyMenuPosition = (isScrollable) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isScrollable) {
      layoutWrap.classList.add('menu-position-scrollable')
      console.log('Applied scrollable menu position')
    } else {
      layoutWrap.classList.remove('menu-position-scrollable')
      console.log('Applied fixed menu position')
    }
  }
}

const applyHeaderPosition = (isScrollable) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isScrollable) {
      layoutWrap.classList.add('header-position-scrollable')
      console.log('Applied scrollable header position')
    } else {
      layoutWrap.classList.remove('header-position-scrollable')
      console.log('Applied fixed header position')
    }
  }
}

const applyLoader = (isEnabled) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isEnabled) {
      layoutWrap.classList.remove('loader-off')
      console.log('Enabled loader')
    } else {
      layoutWrap.classList.add('loader-off')
      console.log('Disabled loader')
    }
  }
}

// Color theme functions
const applyMenuColor = (color) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  const logoHeader = document.querySelector('#logo_header')

  if (layoutWrap) {
    layoutWrap.setAttribute('data-menu-background', `colors-menu-${color}`)
    console.log(`Applied menu color: ${color}`)

    // Update logo based on color
    if (logoHeader) {
      const lightLogo = logoHeader.getAttribute('data-light')
      const darkLogo = logoHeader.getAttribute('data-dark')

      if (color === 'fff') {
        logoHeader.src = darkLogo || logoHeader.src
      } else {
        logoHeader.src = lightLogo || logoHeader.src
      }
    }
  }
}

const applyHeaderColor = (color) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    layoutWrap.setAttribute('data-colors-header', `colors-header-${color}`)
    console.log(`Applied header color: ${color}`)
  }
}

const applyPrimaryColor = (color) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    layoutWrap.setAttribute('data-theme-primary', `theme-primary-${color}`)
    console.log(`Applied primary color: ${color}`)
  }
}

const applyBackgroundColor = (color) => {
  const body = document.body
  if (body) {
    body.setAttribute('data-theme-background', `theme-background-${color}`)
    console.log(`Applied background color: ${color}`)
  }
}

// Clear all settings
const clearAllStyles = () => {
  const layoutWrap = document.querySelector('.layout-wrap')
  const body = document.body

  if (layoutWrap) {
    // Remove layout classes
    layoutWrap.classList.remove(
      'layout-width-boxed',
      'menu-style-icon',
      'menu-style-icon-default',
      'menu-position-scrollable',
      'header-position-scrollable',
      'loader-off'
    )

    // Remove color attributes
    layoutWrap.removeAttribute('data-menu-background')
    layoutWrap.removeAttribute('data-colors-header')
    layoutWrap.removeAttribute('data-theme-primary')
  }

  if (body) {
    body.removeAttribute('data-theme-background')
    body.classList.remove('dark-theme')
  }

  // Reset localStorage
  localStorage.setItem('toggled', 'light-theme')

  console.log('All styles cleared')
}

const clearAllColors = () => {
  const layoutWrap = document.querySelector('.layout-wrap')
  const body = document.body

  if (layoutWrap) {
    layoutWrap.removeAttribute('data-menu-background')
    layoutWrap.removeAttribute('data-colors-header')
    layoutWrap.removeAttribute('data-theme-primary')
  }

  if (body) {
    body.removeAttribute('data-theme-background')
  }

  console.log('All colors cleared')
}

onMounted(() => {
  console.log('Switcher component mounted')
})
</script>

<template>
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight">
    <div class="offcanvas-header">
      <h6 id="offcanvasRightLabel">Setting</h6>
      <button
        type="button"
        class="btn-close text-reset"
        data-bs-dismiss="offcanvas"
        aria-label="Close"
      ></button>
    </div>

    <div class="offcanvas-body">
      <div class="widget-tabs">
        <ul class="widget-menu-tab style-1">
          <li
            :class="['item-title', { active: activeTab === 0 }]"
            @click="switchTab(0)"
          >
            <span class="inner">
              <div class="body-title">Theme Style</div>
            </span>
          </li>

          <li
            :class="['item-title', { active: activeTab === 1 }]"
            @click="switchTab(1)"
          >
            <span class="inner">
              <div class="body-title">Theme Colors</div>
            </span>
          </li>
        </ul>

        <div class="widget-content-tab">
          <div :class="['widget-content-inner', { active: activeTab === 0 }]">
            <form class="form-theme-style">
              <fieldset class="layout-width">
                <div class="body-title mb-5">Layout width style</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="boxed"
                      type="radio"
                      name="width-style"
                      id="width-style2"
                      @change="applyLayoutWidth(true)"
                    />
                    <label for="width-style2" class="">
                      <div class="body-title">Boxed</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="full"
                      type="radio"
                      name="width-style"
                      id="width-style1"
                      @change="applyLayoutWidth(false)"
                      checked
                    />
                    <label for="width-style1" class="">
                      <div class="body-title">Full width</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="menu-style">
                <div class="body-title mb-5">Vertical & Horizontal menu style</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="menu-click"
                      type="radio"
                      name="menu-style"
                      id="menu-style1"
                      @change="applyMenuStyle('default')"
                      checked
                    />
                    <label class="" for="menu-style1">
                      <div class="body-title">Menu click</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="icon-hover"
                      type="radio"
                      name="menu-style"
                      id="menu-style2"
                      @change="applyMenuStyle('icon-hover')"
                    />
                    <label class="" for="menu-style2">
                      <div class="body-title">Icon hover</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="icon-default"
                      type="radio"
                      name="menu-style"
                      id="menu-style3"
                      @change="applyMenuStyle('icon-default')"
                    />
                    <label class="" for="menu-style3">
                      <div class="body-title">Icon default</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="menu-position">
                <div class="body-title mb-5">Menu position</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="menu-fixed"
                      type="radio"
                      name="menu-position"
                      id="menu-position1"
                      @change="applyMenuPosition(false)"
                      checked
                    />
                    <label class="" for="menu-position1">
                      <div class="body-title">Fixed</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="menu-scrollable"
                      type="radio"
                      name="menu-position"
                      id="menu-position2"
                      @change="applyMenuPosition(true)"
                    />
                    <label class="" for="menu-position2">
                      <div class="body-title">Scrollable</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="header-position">
                <div class="body-title mb-5">Header positions</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="header-fixed"
                      type="radio"
                      name="header-positions"
                      id="header-positions1"
                      @change="applyHeaderPosition(false)"
                      checked
                    />
                    <label class="" for="header-positions1">
                      <div class="body-title">Fixed</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="header-scrollable"
                      type="radio"
                      name="header-positions"
                      id="header-positions2"
                      @change="applyHeaderPosition(true)"
                    />
                    <label class="" for="header-positions2">
                      <div class="body-title">Scrollable</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="style-loader">
                <div class="body-title mb-5">Loader</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="style-loader-on"
                      type="radio"
                      name="loader"
                      id="loader1"
                      @change="applyLoader(true)"
                      checked
                    />
                    <label class="" for="loader1">
                      <div class="body-title">Enable</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="style-loader-off"
                      type="radio"
                      name="loader"
                      id="loader2"
                      @change="applyLoader(false)"
                    />
                    <label class="" for="loader2">
                      <div class="body-title">Disable</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <div
                class="tf-button style-1 label-01 w-100 cursor-pointer w-full button-clear-select"
                @click="clearAllStyles"
              >
                Clear all
              </div>
            </form>
          </div>

          <div :class="['widget-content-inner', { active: activeTab === 1 }]">
            <form class="form-theme-color">
              <fieldset class="menu-color">
                <div class="body-title mb-10">Menu Background color</div>

                <div class="select-colors-theme colors-menu mb-10">
                  <div
                    class="item color-161326 active default"
                    @click="applyMenuColor('161326')"
                  ></div>

                  <div
                    class="item color-1E293B"
                    @click="applyMenuColor('1E293B')"
                  ></div>

                  <div
                    class="item color-fff"
                    @click="applyMenuColor('fff')"
                  ></div>

                  <div
                    class="item color-3A3043"
                    @click="applyMenuColor('3A3043')"
                  ></div>
                </div>

                <div class="text-tiny">
                  Note:If you want to change color Menu dynamically change from below Theme Primary
                  color picker
                </div>
              </fieldset>

              <fieldset class="">
                <div class="body-title mb-10">Header Background color</div>

                <div class="select-colors-theme colors-header mb-10">
                  <div
                    class="item color-fff active default"
                    @click="applyHeaderColor('fff')"
                  ></div>

                  <div
                    class="item color-1E293B"
                    @click="applyHeaderColor('1E293B')"
                  ></div>

                  <div
                    class="item color-161326"
                    @click="applyHeaderColor('161326')"
                  ></div>

                  <div
                    class="item color-3A3043"
                    @click="applyHeaderColor('3A3043')"
                  ></div>
                </div>

                <div class="text-tiny">
                  Note:If you want to change color Header dynamically change from below Theme
                  Primary color picker
                </div>
              </fieldset>

              <fieldset class="">
                <div class="body-title mb-10">Theme Primary color</div>

                <div class="select-colors-theme colors-theme-primary mb-10">
                  <div
                    class="item color-161326 active default"
                    @click="applyPrimaryColor('161326')"
                  ></div>

                  <div
                    class="item color-2377FC"
                    @click="applyPrimaryColor('2377FC')"
                  ></div>

                  <div
                    class="item color-35988D"
                    @click="applyPrimaryColor('35988D')"
                  ></div>

                  <div
                    class="item color-7047D6"
                    @click="applyPrimaryColor('7047D6')"
                  ></div>
                </div>
              </fieldset>

              <fieldset class="">
                <div class="body-title mb-10">Theme Background color</div>

                <div class="select-colors-theme colors-theme-background mb-10">
                  <div
                    class="item color-FFFFFF active default"
                    @click="applyBackgroundColor('FFFFFF')"
                  ></div>

                  <div
                    class="item color-252E3A"
                    @click="applyBackgroundColor('252E3A')"
                  ></div>

                  <div
                    class="item color-1E1D2A"
                    @click="applyBackgroundColor('1E1D2A')"
                  ></div>

                  <div
                    class="item color-1B2627"
                    @click="applyBackgroundColor('1B2627')"
                  ></div>
                </div>
              </fieldset>
              <div
                class="tf-button style-1 label-01 w-100 cursor-pointer w-full button-clear-select"
                @click="clearAllColors"
              >
                Clear all
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
