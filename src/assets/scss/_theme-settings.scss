.layout-wrap {
    overflow: hidden;
    @include transition3;

    &.menu-position-scrollable {
        @include d-flex;

        &.full-width {
            margin-left: -256px;
        }

        .section-menu-left {
            position: relative;
            height: unset;
            border: 0;
            .box-logo {
                background-color: var(--Primary);
            }
        }

        .section-content-right {
            .main-content {
                padding-left: 0;
            }
        }
    }

    &.header-position-scrollable {
        .header-dashboard {
            position: relative;
            width: 100%;
            padding-left: calc(256px + 48px);
        }

        .main-content {
            padding-top: 0;
        }

        &.menu-position-scrollable {
            &.full-width {
                margin-left: -256px;
            }

            .header-dashboard {
                padding-left: 48px;
            }
        }
    }

    &.layout-width-boxed {
        max-width: 1440px;
        margin: auto;

        .tf-container {
            max-width: 1112px;
        }

        .section-menu-left {
            left: calc((100vw - 1440px) / 2 - 8px);

            >.box-logo {
                left: calc((100vw - 1440px) / 2 - 7px);
            }
        }

        .header-dashboard {
            max-width: 1440px;
            width: 100%;
            right: 50%;
            @include center(50%, 0);
            padding-left: calc(256px + 48px);
        }

        .main-content-inner {
            padding-right: 0;
        }

        &.full-width {
            .section-menu-left {
                left: calc((100vw - 1440px) / 2 - 288px);
                opacity: 0;
                visibility: hidden;
            }

            .box-logo {
                left: calc((100vw - 1440px) / 2 - 288px);
                opacity: 0;
                visibility: hidden;
            }

            .main-content-inner {
                padding-left: 0;
            }
        }

        &.header-position-scrollable {
            &.menu-position-scrollable {
                .section-menu-left {
                    left: 0;
                }

                &.full-width {
                    margin: auto;

                    .section-menu-left {
                        left: calc((100vw - 1440px) / 2 - 288px);

                        >.box-logo {
                            left: calc((100vw - 1440px) / 2 - 288px);
                        }
                    }

                    .section-content-right {
                        margin-left: -256px;
                    }
                }
            }
        }

        &.menu-position-scrollable {
            .section-menu-left {
                left: 0;
            }

            &.full-width {
                margin: auto;

                .section-menu-left {
                    left: calc((100vw - 1440px) / 2 - 288px);

                    >.box-logo {
                        left: calc((100vw - 1440px) / 2 - 288px);
                    }
                }

                .section-content-right {
                    margin-left: -256px;
                }
            }
        }

        &.menu-style-icon {
            .header-dashboard {
                padding-left: 122px;
                width: 100%;
            }
        }
    }

    &.menu-style-icon {
        .section-menu-left {
            width: 75px;
            min-width: 75px;

            .box-logo {
                width: 75px;
                padding: 15px;

                a {
                    overflow: hidden;

                    img {
                        min-width: 154px;
                        width: 154px;
                    }
                }

                .button-show-hide {
                    display: none;
                }
            }

            .bottom,
            .center-heading {
                display: none;
            }

            .menu-item {
                > a {
                    padding: 13px 14px 14px 24px;
                    > .text {
                        display: none;
                    }
                }

                &::after {
                    display: none;
                }

                &.has-children.active .sub-menu {
                    display: none !important;
                }
            }

            &:hover {
                width: 256px;
                min-width: 256px;

                .box-logo {
                    width: 256px;
                }

                .menu-item {
                    >a {
                        >.text {
                            display: block;
                        }
                    }

                    >.sub-menu {
                        display: none;
                    }

                    &::after {
                        display: unset;
                    }

                    &.active {
                        >.sub-menu {
                            display: block !important;
                        }
                    }
                }
            }
        }

        .header-dashboard {
            width: calc(100% - 75px);
        }

        .main-content {
            padding-left: 75px;
        }

        &.menu-position-scrollable {
            .main-content {
                padding-left: 0px;
            }
        }

        &.header-position-scrollable {
            .header-dashboard {
                width: 100%;
                padding-left: 122px;
            }

            &.menu-position-scrollable {
                .header-dashboard {
                    padding-left: 30px;
                }
            }
        }
    }

    &.menu-style-icon-default {
        .header-dashboard {
            width: calc(100% - 75px);
        }

        .main-content {
            padding-left: 75px;
        }

        .section-menu-left {
            width: 75px;
            min-width: 75px;

            .box-logo {
                width: 75px;
                padding: 15px;

                a {
                    overflow: hidden;

                    img {
                        min-width: 154px;
                        width: 154px;
                    }
                }

                .button-show-hide {
                    display: none;
                }
            }

            .bottom,
            .center-heading {
                display: none;
            }

            .menu-item {
                > a {
                    padding: 13px 14px 14px 24px;
                    > .text {
                        display: none;
                    }
                }

                &::after {
                    display: none;
                }

                &.has-children.active .sub-menu {
                    display: none !important;
                }
            }

            &:hover .menu-item .menu-item-button.active::before {
                right: -181px;
            }
        }

        &.menu-position-scrollable {
            .main-content {
                padding-left: 0px;
            }
        }

        &.header-position-scrollable {
            .header-dashboard {
                width: 100%;
                padding-left: 122px;
            }

            &.menu-position-scrollable {
                .header-dashboard {
                    padding-left: 30px;
                }
            }
        }
    }

    &.loader-off {
        #preload {
            display: none;
        }
    }
}

@media (min-width: 1201px) {
    .layout-wrap {
        &.menu-style-icon-default {
            &.full-width {
                .section-menu-left {
                    left: 0;

                    >.box-logo {
                        left: 0;
                    }
                }
            }

            .section-menu-left {
                width: 75px;
                min-width: 75px;
                @include transition0;
                &::before {
                    width: 75px;
                }
                >.box-logo {
                    width: 75px;
                    padding: 15px;

                    a {
                        overflow: hidden;

                        img {
                            min-width: 154px;
                            width: 154px;
                        }
                    }

                    .button-show-hide {
                        display: none;
                    }
                }

                &:hover {
                    width: 256px;
                    background-color: transparent;
                    box-shadow: none;
                    align-items: start;
                    >.box-logo {
                        width: 256px;
                    }
                    &::before {
                        width: 256px;
                    }
                    .center {
                        background-color: var(--Primary);
                        width: 75px;
                        flex-grow: 1;
                    }
                }

                .center-item {
                    position: relative;

                    .menu-item {
                        >a {
                            >.text {
                                display: none;
                            }
                        }

                        &::after {
                            display: none;
                        }

                        &.has-children {
                            .sub-menu {
                                width: 181px;
                                position: absolute;
                                left: 100%;
                                top: 0px;
                                display: block !important;
                                background-color: var(--Gainsboro);
                                @include transition3;
                                opacity: 0;
                                visibility: hidden;
                                z-index: -1;
                                height: unset !important;
                                margin-top: 0 !important;
                                margin-bottom: 0 !important;
                                padding-top: 0px !important;
                                padding-bottom: 0px !important;
                                padding-right: 16px !important;
                                .sub-menu-item {
                                    padding-left: 40px;
                                }
                            }

                            &:hover {
                                i,
                                .text {
                                    color: var(--Primary) !important;
                                }
                                > a {
                                    background-color: var(--Gainsboro);
                                }
                                .sub-menu {
                                    opacity: 1;
                                    visibility: visible;
                                }

                            }
                        }
                    }

                }

            }

            &.layout-width-boxed {
                .header-dashboard {
                    padding-left: 122px;
                }

                &.menu-position-scrollable {
                    .main-content {
                        padding-left: 0px;
                    }

                    .section-menu-left {
                        >.box-logo {
                            left: calc((100vw - 1454px) / 2);
                        }
                    }

                    &.header-position-scrollable {
                        .header-dashboard {
                            width: 100%;
                            padding-left: 30px;
                        }
                    }
                }

                &.header-position-scrollable {
                    .header-dashboard {
                        width: 100%;
                    }
                }
            }

            &.header-position-scrollable {
                .header-dashboard {
                    width: 100%;
                    padding-left: 122px;
                }
            }

            &.menu-position-scrollable {
                .main-content {
                    padding-left: 0;
                }

                .section-menu-left {
                    &:hover {
                        margin-right: -181px;
                    }
                }

                &.header-position-scrollable {
                    .header-dashboard {
                        padding-left: 30px;
                    }
                }
            }
        }
    }
}

@media (max-width: 1440px) {
    .layout-wrap {
        &.menu-style-icon-default {
            &.layout-width-boxed {
                .header-dashboard {
                    width: 100%;
                }
            }
        }

        &.layout-width-boxed {
            .section-menu-left {
                left: 0;

                >.box-logo {
                    left: 0;
                }
            }

            .main-content-inner {
                padding-left: 30px;
                padding-right: 13px;
            }

            &.full-width {
                .main-content-inner {
                    padding-left: 30px;
                }
            }

            &.menu-position-scrollable {
                >.box-logo {
                    left: 0;
                }
            }
        }
    }
}

@media (max-width: 1200px) {
    .layout-wrap {
        &.header-position-scrollable {
            &.full-width {
                .header-dashboard {
                    padding-left: 15px !important;
                }
            }

            &.menu-position-scrollable {
                &.full-width {
                    .section-menu-left {
                        left: 256px;
                    }
                }
            }
        }

        &.menu-position-scrollable {
            margin-left: -256px;

            &.full-width {
                .section-menu-left {
                    left: 256px;
                }

                .header-dashboard {
                    padding-left: 15px !important;
                }
            }
        }

        &.menu-style-icon {
            &.full-width {
                .header-dashboard {
                    padding-left: 15px !important;
                }
            }

            .section-menu-left {
                width: 256px;
                min-width: 256px;

                .box-logo {
                    width: 256px;

                    .button-show-hide,
                    .logo-full {
                        display: block;
                    }

                    .logo-icon {
                        display: none;
                    }
                }

                .menu-item {
                    >a {
                        justify-content: start;

                        >.text {
                            display: block;
                        }
                    }

                    >.sub-menu {
                        display: none;
                    }

                    &::after {
                        display: unset;
                    }

                    &.active {
                        >.sub-menu {
                            display: block !important;
                        }
                    }
                }

            }

            &.menu-position-scrollable {
                margin-left: 0;

                .section-menu-left {
                    position: fixed;
                }

                &.full-width {
                    .section-menu-left {
                        left: 0;
                    }
                }

                &.header-position-scrollable {
                    margin-left: 0;
                }
            }
        }

        &.layout-width-boxed {
            .section-menu-left {
                left: -100%;

                .box-logo {
                    left: -100%;
                }
            }

            .section-content-right {
                .header-dashboard {
                    right: 0;
                    @include center(0, 0);
                }

                .main-content {
                    .main-content-inner {
                        padding-left: 15px;
                    }
                }
            }

            &.full-width {
                .section-menu-left {
                    left: 0;
                    opacity: 1;
                    visibility: visible;
                }

                .box-logo {
                    left: 0;
                    opacity: 1;
                    visibility: visible;
                }

                .header-dashboard {
                    padding-left: 15px !important;
                }

                .main-content {
                    .main-content-inner {
                        padding-left: 15px;
                    }
                }
            }

            &.header-position-scrollable {
                &.menu-position-scrollable {
                    margin-left: -256px;

                    .section-menu-left {
                        left: -256px;

                        .box-logo {
                            left: -256px;
                        }
                    }

                    &.full-width {
                        margin-left: 0;

                        .section-menu-left {
                            left: 0;

                            .box-logo {
                                left: 0;
                            }
                        }
                    }
                }
            }

            &.menu-position-scrollable {
                margin-left: 0;

                .section-menu-left {
                    left: -100%;

                    >.box-logo {
                        left: -100%;
                    }
                }

                .section-content-right {
                    .main-content {
                        margin-left: -256px;
                    }
                }

                &.full-width {
                    .section-menu-left {
                        left: 0;

                        >.box-logo {
                            left: 0;
                        }
                    }

                    .section-content-right {
                        .main-content {
                            margin-left: 0;
                        }
                    }
                }
            }

            &.menu-style-icon {
                &.menu-position-scrollable {
                    .main-content {
                        margin-left: 0;
                    }

                    &.full-width {
                        .section-content-right {
                            margin-left: 0;
                        }
                    }

                    &.header-position-scrollable {
                        margin-left: 0;
                    }
                }
            }
        }

        &.menu-style-icon-default {
            &.layout-width-boxed {
                &.menu-position-scrollable {
                    &.header-position-scrollable {
                        margin-left: 0;

                        .header-dashboard {
                            position: unset;
                            width: unset !important;
                            ;
                            margin-left: -256px;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 991px) {

    .menu-position {
        display: none;
    }
}

// colors-menu
[data-menu-background="colors-menu-fff"] {
    .section-menu-left {
        &::before {
            background: #fff !important;
            opacity: 1;
        }

        .box-logo {
            background-color: #fff !important;
        }

        .center {
            .menu-item {
                a {
                    .icon {
                        i {
                            color: #A4A4A9 !important;
                        }

                        svg {
                            path {
                                stroke: #A4A4A9 !important;
                            }
                        }
                    }

                    .text {
                        color: #A4A4A9;
                    }

                    &:hover {
                        .icon {
                            i {
                                color: #000 !important;
                            }

                            svg {
                                path {
                                    stroke: #000 !important;
                                }
                            }
                        }

                        .text {
                            color: #000 !important;
                        }
                    }
                }

                &.has-children {
                    .sub-menu-item.active {
                        a {
                            .text {
                                color: #161326;
                            }
                        }
                    }

                    &::after {
                        color: #161326 !important;
                    }
                }
            }
        }

    }
}

[data-menu-background="colors-menu-1E293B"] {
    .section-menu-left {
        &::before {
            background: #1E293B !important;
            opacity: 1;
        }

        .box-logo {
            background: #1E293B !important;
        }

    }
}

[data-menu-background="colors-menu-161326"] {
    .section-menu-left {
        &::before {
            background: #161326 !important;
            opacity: 1;
        }

    }
}

[data-menu-background="colors-menu-3A3043"] {
    .section-menu-left {
        &::before {
            background: #3A3043 !important;
            opacity: 1;
        }

        .box-logo {
            background-color: #3A3043 !important;
        }

    }
}

// colors-header
[data-colors-header="colors-header-fff"] {
    .section-content-right {
        .header-dashboard {
            background-color: #fff;
        }
    }
}

[data-colors-header="colors-header-1E293B"] {
    .section-content-right {
        .header-dashboard {
            background-color: #1E293B;

            .wg-user .name,
            h6 {
                color: var(--White);
            }

            .form-search {
                input {
                    color: #fff;
                    background-color: #1E293B;
                    &::placeholder {
                        color: #fff;
                    }
                }

                .button-submit {
                    i {
                        color: #fff;
                    }
                }
            }

            .header-item {
                background-color: rgba(203, 213, 225, 0.1019607843);
                svg path {
                    fill: #fff;
                }
                i {
                    color: #fff;
                }
            }

            .setting {
                i {
                    color: #fff;
                }
            }

            .header-user {
                .body-text,
                .text-tiny,
                .body-title {
                    color: #fff !important;
                }
            }
        }
    }
}

[data-colors-header="colors-header-161326"] {
    .section-content-right {
        .header-dashboard {
            background-color: #161326;

            .wg-user .name,
            h6 {
                color: var(--White);
            }

            .form-search {
                input {
                    color: #fff;
                    background-color: #161326;
                    &::placeholder {
                        color: #fff;
                    }
                }

                .button-submit {
                    i {
                        color: #fff;
                    }
                }
            }

            .header-item {
                background-color: rgba(203, 213, 225, 0.1019607843);
                svg path {
                    fill: #fff;
                }
                i {
                    color: #fff;
                }
            }

            .setting {
                i {
                    color: #fff;
                }
            }

            .header-user {
                .body-text,
                .text-tiny,
                .body-title {
                    color: #fff !important;
                }
            }
        }
    }
}

[data-colors-header="colors-header-3A3043"] {
    .section-content-right {
        .header-dashboard {
            background-color: #3A3043;

            .wg-user .name,
            h6 {
                color: var(--White);
            }

            .form-search {
                input {
                    color: #fff;
                    background-color: #3A3043;
                    &::placeholder {
                        color: #fff;
                    }
                }

                .button-submit {
                    i {
                        color: #fff;
                    }
                }
            }

            .header-item {
                background-color: rgba(203, 213, 225, 0.1019607843);
                svg path {
                    fill: #fff;
                }
                i {
                    color: #fff;
                }
            }

            .setting {
                i {
                    color: #fff;
                }
            }

            .header-user {
                .body-text,
                .text-tiny,
                .body-title {
                    color: #fff !important;
                }
            }
        }
    }
}

// theme-primary
[data-theme-primary="theme-primary-2377FC"] {
    --YellowGreen: #2377FC;
}

[data-theme-primary="theme-primary-161326"] {
    --YellowGreen: #161326;
}

[data-theme-primary="theme-primary-35988D"] {
    --YellowGreen: #35988D;
}

[data-theme-primary="theme-primary-7047D6"] {
    --YellowGreen: #7047D6;
}

// theme-background
[data-theme-background="theme-background-FFFFFF"] {
    background-color: #FFFFFF;

    .section-content-right .main-content {
        background-color: #FFFFFF;
    }
}

[data-theme-background="theme-background-252E3A"] {
    background-color: #252E3A;

    .section-content-right .main-content {
        background-color: #252E3A;
    }
}

[data-theme-background="theme-background-1E1D2A"] {
    background-color: #1E1D2A;

    .section-content-right .main-content {
        background-color: #1E1D2A;
    }
}

[data-theme-background="theme-background-1B2627"] {
    background-color: #1B2627;

    .section-content-right .main-content {
        background-color: #1B2627;
    }
}
