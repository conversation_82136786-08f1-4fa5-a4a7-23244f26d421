import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ability } from '@/abilities'
import { subjects, actions } from '@/abilities'
import HomeView from '../views/HomeView.vue'

/*import DefaultLayout from '../layouts/DefaultLayout.vue'
import AuthLayout    from '../layouts/AuthLayout.vue'
import AdminLayout   from '../layouts/AdminLayout.vue'

const routes = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      { path: '',       component: HomePage },
      { path: 'about',  component: () => import('../views/About.vue') },
      // …etc.
    ]
  },*/

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: 'Home',
        description: 'Welcome to your dashboard home page',
      },
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: {
        title: 'About Us',
        description: 'Learn more about our company and team',
      },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        requiresGuest: true, // Only accessible when not authenticated
        title: 'Login',
        description: 'Sign in to your account',
      },
    },
    {
      path: '/sign-in',
      name: 'sign-in',
      component: () => import('../views/auth/SignIn.vue'),
      meta: {
        requiresGuest: true, // Only accessible when not authenticated
      },
    },
    {
      path: '/sign-up',
      name: 'sign-up',
      component: () => import('../views/auth/SignUp.vue'),
      meta: {
        requiresGuest: true, // Only accessible when not authenticated
      },
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: {
        requiresAuth: true,
        requiresPermission: {
          action: actions.READ,
          subject: subjects.ADMIN_PANEL,
        },
        title: 'Admin Panel',
        description: 'Administrative panel for system management',
      },
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/UserView.vue'),
      meta: {
        requiresAuth: true,
        title: 'Dashboard',
        description: 'View your dashboard analytics and overview',
      },
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/UserView.vue'),
      meta: {
        requiresAuth: true,
        title: 'Profile',
        description: 'Manage your user profile and settings',
      },
    },
    {
      path: '/test-head',
      name: 'test-head',
      component: () => import('../views/TestHeadView.vue'),
      meta: {
        title: 'Test Head',
        description: 'Test page for head management functionality',
      },
    },
    {
      path: '/theme-test',
      name: 'theme-test',
      component: () => import('../views/ThemeTestView.vue'),
      meta: {
        title: 'Theme Test',
        description: 'Test page for theme switching functionality',
      },
    },
    {
      path: '/layout-test',
      name: 'layout-test',
      component: () => import('../views/LayoutTestView.vue'),
      meta: {
        title: 'Layout Test',
        description: 'Test page for layout switching functionality',
      },
    },
  ],
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const isAuthenticated = authStore.isAuthenticated

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && isAuthenticated) {
    return next('/')
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !isAuthenticated) {
    return next('/login')
  }

  // Check if route requires specific permissions
  if (to.meta.requiresPermission && isAuthenticated) {
    const { action, subject } = to.meta.requiresPermission
    if (!ability.can(action, subject)) {
      // Redirect to appropriate page based on user role
      if (authStore.userRole === 'admin') {
        return next('/')
      } else {
        return next('/dashboard')
      }
    }
  }

  next()
})

export default router
